import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, Index, ManyToOne, OneToMany } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { StatusCommon } from '../../../commons/enums';
import { DBIndexes } from '../../../commons/consts/db.const';
import { Company } from '../../company/company.entity';
import { User } from '../../user/user.entity';
import { RiskMatrixCell } from './risk-matrix-cell.entity';
import { RiskValueMapping } from './risk-value-mapping.entity';
import { RiskLevelMapping } from './risk-level-mapping.entity';

@Entity()
@Index(DBIndexes.IDX_RISK_MATRIX_COMPANYID, ['companyId'], {
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_RISK_MATRIX_CODE_COMPANYID, ['matrixCode', 'companyId'], {
  unique: true,
  where: 'deleted = false',
})
export class RiskMatrixMaster extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'varchar', length: 50 })
  public matrixCode: string;

  @Column({ type: 'int' })
  public rows: number;

  @Column({ type: 'int' })
  public columns: number;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE })
  public status: string;

  @Column({ type: 'simple-array', nullable: true })
  public rowsName: string[];

  @Column({ type: 'simple-array', nullable: true })
  public columnsName: string[];

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  // Relationships
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;

  @OneToMany(()=>RiskMatrixCell, (riskMatrixCell)=>riskMatrixCell.riskMatrix, { cascade: true })
  cells: RiskMatrixCell[];

  @OneToMany(()=>RiskValueMapping, (riskValueMapping)=>riskValueMapping.riskMatrix, { cascade: true })
  valueMappings: RiskValueMapping[];

  @OneToMany(()=>RiskLevelMapping, (riskLevelMapping)=>riskLevelMapping.riskMatrix, { cascade: true })
  levelMappings: RiskLevelMapping[];
}