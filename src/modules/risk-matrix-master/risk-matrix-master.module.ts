import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RiskMatrixMasterController } from './risk-matrix-master.controller';
import { RiskMatrixMasterService } from './risk-matrix-master.service';
import { RiskMatrixMasterRepository } from './risk-matrix-master.repository';
import { RiskMatrixCell } from './entities/risk-matrix-cell.entity';
import { RiskValueMapping } from './entities/risk-value-mapping.entity';
import { RiskLevelMapping } from './entities/risk-level-mapping.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RiskMatrixMasterRepository,
      RiskMatrixCell,
      RiskValueMapping,
      RiskLevelMapping,
    ]),
  ],
  controllers: [RiskMatrixMasterController],
  providers: [RiskMatrixMasterService],
  exports: [],
})
export class RiskMatrixMasterModule {}